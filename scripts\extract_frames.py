import os
import cv2
from pathlib import Path

# =============================================================================
# VIDEO FRAME EXTRACTION UTILITY
#
# Author: Dr<PERSON> (AI Assistant) & <PERSON><PERSON>  
# Date: July 26, 2025
#
# Purpose: Extract frames from video clips for image-based annotation
# =============================================================================

def extract_frames_from_video(video_path, output_dir, frame_interval=30, max_frames=None):
    """
    Extract frames from a video file.
    
    Args:
        video_path (str): Path to the video file
        output_dir (str): Directory to save extracted frames
        frame_interval (int): Extract every Nth frame (default: 30 = ~1 frame per second at 30fps)
        max_frames (int): Maximum number of frames to extract (None = no limit)
    
    Returns:
        list: Paths to extracted frame images
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video: {video_path}")
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    print(f"📹 Video info:")
    print(f"   • FPS: {fps:.2f}")
    print(f"   • Total frames: {total_frames}")
    print(f"   • Duration: {duration:.2f} seconds")
    print(f"   • Extracting every {frame_interval} frames")
    
    extracted_paths = []
    frame_count = 0
    extracted_count = 0
    
    video_name = Path(video_path).stem
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Extract frame at specified interval
        if frame_count % frame_interval == 0:
            if max_frames and extracted_count >= max_frames:
                break
                
            # Generate frame filename
            timestamp = frame_count / fps
            frame_filename = f"{video_name}_frame_{frame_count:06d}_t{timestamp:.2f}s.jpg"
            frame_path = os.path.join(output_dir, frame_filename)
            
            # Save frame
            cv2.imwrite(frame_path, frame)
            extracted_paths.append(frame_path)
            extracted_count += 1
            
            if extracted_count % 10 == 0:
                print(f"   Extracted {extracted_count} frames...")
        
        frame_count += 1
    
    cap.release()
    print(f"✅ Extracted {extracted_count} frames to: {output_dir}")
    return extracted_paths

def extract_frames_from_clips(clips_dir, output_base_dir, frame_interval=30, max_frames_per_clip=None):
    """
    Extract frames from all video clips in a directory.
    
    Args:
        clips_dir (str): Directory containing video clips
        output_base_dir (str): Base directory for extracted frames
        frame_interval (int): Extract every Nth frame
        max_frames_per_clip (int): Maximum frames per clip
    
    Returns:
        dict: Mapping of clip names to extracted frame paths
    """
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    
    # Find all video files
    video_files = []
    for ext in video_extensions:
        video_files.extend(Path(clips_dir).glob(f"*{ext}"))
        video_files.extend(Path(clips_dir).glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ No video files found in {clips_dir}")
        return {}
    
    print(f"🎬 Found {len(video_files)} video files")
    
    all_extracted = {}
    
    for video_file in video_files:
        clip_name = video_file.stem
        output_dir = os.path.join(output_base_dir, clip_name)
        
        print(f"\n📹 Processing: {video_file.name}")
        
        try:
            extracted_paths = extract_frames_from_video(
                str(video_file),
                output_dir,
                frame_interval,
                max_frames_per_clip
            )
            all_extracted[clip_name] = extracted_paths
            
        except Exception as e:
            print(f"❌ Error processing {video_file.name}: {e}")
            continue
    
    return all_extracted

def main():
    """Main function for frame extraction."""
    print("🎬 Video Frame Extraction Utility")
    print("=" * 40)
    
    # Configuration
    PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # Option 1: Extract from critical clips
    critical_clips_dir = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical")
    normal_clips_dir = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "normal")
    
    # Output directory for extracted frames
    frames_output_dir = os.path.join(PROJECT_ROOT, "images")
    
    print("Choose extraction mode:")
    print("1. Extract from critical clips only")
    print("2. Extract from normal clips only") 
    print("3. Extract from all clips")
    print("4. Extract from specific video file")
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        print(f"\n📁 Extracting frames from critical clips: {critical_clips_dir}")
        extract_frames_from_clips(
            critical_clips_dir,
            os.path.join(frames_output_dir, "critical"),
            frame_interval=30,  # ~1 frame per second
            max_frames_per_clip=100  # Limit frames per clip
        )
        
    elif choice == "2":
        print(f"\n📁 Extracting frames from normal clips: {normal_clips_dir}")
        extract_frames_from_clips(
            normal_clips_dir,
            os.path.join(frames_output_dir, "normal"),
            frame_interval=60,  # ~0.5 frames per second for normal clips
            max_frames_per_clip=50
        )
        
    elif choice == "3":
        print(f"\n📁 Extracting frames from all clips")
        # Critical clips
        extract_frames_from_clips(
            critical_clips_dir,
            os.path.join(frames_output_dir, "critical"),
            frame_interval=30,
            max_frames_per_clip=100
        )
        # Normal clips
        extract_frames_from_clips(
            normal_clips_dir,
            os.path.join(frames_output_dir, "normal"),
            frame_interval=60,
            max_frames_per_clip=50
        )
        
    elif choice == "4":
        video_path = input("Enter path to video file: ").strip()
        if not os.path.exists(video_path):
            print(f"❌ Video file not found: {video_path}")
            return
            
        output_dir = input("Enter output directory for frames: ").strip()
        frame_interval = int(input("Enter frame interval (default 30): ") or "30")
        
        extract_frames_from_video(video_path, output_dir, frame_interval)
        
    else:
        print("❌ Invalid choice")
        return
    
    print(f"\n✅ Frame extraction complete!")
    print(f"📁 Frames saved to: {frames_output_dir}")
    print("\n🚀 You can now run the image annotation script:")
    print("   python scripts/yolo_world_image_annotator.py")

if __name__ == "__main__":
    main()
