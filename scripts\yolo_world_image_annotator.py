import os
import shutil
import fiftyone as fo
from ultralytics import <PERSON>OLO
import cv2
from pathlib import Path

# =============================================================================
# YOLO-WORLD IMAGE AUTO-LABELING SCRIPT
#
# Author: Dr. <PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Purpose: Auto-label image datasets using YOLO-World before manual correction
# =============================================================================

# ---
# Configuration
# ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
IMAGES_DIR = os.path.join(PROJECT_ROOT, "images")  # Create this directory and add your images
DATASET_NAME = "cts_auto_master"
LABEL_FIELD = "ground_truth"
PREDICTIONS_FIELD = "predictions"

# YOLO-World detection prompts
AUTO_LABEL_PROMPTS = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# Complete class list for annotation
ALL_CLASSES = AUTO_LABEL_PROMPTS + ["DontCare"]

# Vehicle-specific attributes
ATTRIBUTES = {
    "car": ["is_braking", "turn_signal", "hazard_lights"],
    "truck": ["is_braking", "turn_signal", "hazard_lights"], 
    "bus": ["is_braking", "turn_signal", "hazard_lights"],
    "motorcycle": ["is_braking", "turn_signal", "hazard_lights"],
}

# YOLO-World settings
MODEL_PATH = os.path.join(PROJECT_ROOT, "yolov8l-worldv2.pt")
CONFIDENCE_THRESHOLD = 0.3
IOU_THRESHOLD = 0.5
MAX_DETECTIONS = 300

def create_images_directory():
    """Create images directory if it doesn't exist and provide instructions."""
    if not os.path.exists(IMAGES_DIR):
        os.makedirs(IMAGES_DIR)
        print(f"📁 Created images directory: {IMAGES_DIR}")
        print("📋 Please add your images to this directory before running the script.")
        return False
    return True

def init_dataset(image_paths):
    """Initialize FiftyOne dataset from image paths."""
    if fo.dataset_exists(DATASET_NAME):
        print(f"🗑️  Deleting existing dataset '{DATASET_NAME}'")
        fo.delete_dataset(DATASET_NAME)
    
    print(f"📸 Creating dataset '{DATASET_NAME}' with {len(image_paths)} images")
    dataset = fo.Dataset(DATASET_NAME)
    
    # Add samples
    samples = []
    for img_path in image_paths:
        sample = fo.Sample(filepath=img_path)
        samples.append(sample)
    
    dataset.add_samples(samples)
    dataset.persistent = True
    return dataset

def auto_label_with_yolo_world(dataset):
    """Apply YOLO-World auto-labeling to the dataset."""
    print(f"🤖 Loading YOLO-World model: {MODEL_PATH}")
    model = YOLO(MODEL_PATH)
    
    # Set custom classes
    model.set_classes(AUTO_LABEL_PROMPTS)
    print(f"📋 Detection classes: {AUTO_LABEL_PROMPTS}")
    
    print(f"🔍 Running auto-labeling (confidence >= {CONFIDENCE_THRESHOLD})...")
    
    # Apply model to dataset
    dataset.apply_model(
        model,
        label_field=PREDICTIONS_FIELD,
        confidence_thresh=CONFIDENCE_THRESHOLD,
        iou_thresh=IOU_THRESHOLD,
        max_dets=MAX_DETECTIONS
    )
    
    # Clone predictions to ground truth for editing
    dataset.clone_sample_field(PREDICTIONS_FIELD, LABEL_FIELD)
    
    # Count detections
    total_detections = sum(
        len(sample[PREDICTIONS_FIELD].detections) 
        for sample in dataset 
        if sample[PREDICTIONS_FIELD] is not None
    )
    
    print(f"✅ Auto-labeling complete! Generated {total_detections} detections")
    return dataset

def launch_annotation_interface(dataset):
    """Launch CVAT annotation interface for manual correction."""
    print("\n🎯 --- MANUAL ANNOTATION PHASE ---")
    
    # Launch FiftyOne App
    session = fo.launch_app(dataset, auto=False)
    
    # Configure CVAT annotation
    label_schema = {
        LABEL_FIELD: {
            "type": "detections",
            "classes": ALL_CLASSES,
            "attributes": ATTRIBUTES
        }
    }
    
    print("🚀 Launching CVAT annotation interface...")
    dataset.annotate(
        "cts-image-correction-task",
        label_schema=label_schema,
        launch_editor=True
    )
    
    print("🌐 CVAT has been launched in your browser.")
    print("📝 Please review and correct the auto-generated labels:")
    print("   • Fix incorrect bounding boxes")
    print("   • Add missing objects")
    print("   • Set vehicle attributes (braking, turn signals, etc.)")
    print("   • Add 'DontCare' labels for ambiguous objects")
    print(">>> When finished, close the FiftyOne App window to continue.")
    
    session.wait()

def export_datasets(dataset):
    """Export the annotated dataset to YOLO and KITTI formats."""
    print("\n📦 --- DATASET EXPORT PHASE ---")
    
    # Count final annotations
    total_annotations = sum(
        len(sample[LABEL_FIELD].detections) 
        for sample in dataset 
        if sample[LABEL_FIELD] is not None
    )
    print(f"📊 Final dataset contains {total_annotations} annotations")
    
    # Export to YOLO format
    yolo_export_dir = os.path.join(PROJECT_ROOT, "yolo_dataset")
    if os.path.exists(yolo_export_dir):
        shutil.rmtree(yolo_export_dir)
    
    print(f"🎯 Exporting to YOLO format: {yolo_export_dir}")
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=LABEL_FIELD,
        classes=ALL_CLASSES,
        split=0.8  # 80% train, 20% val
    )
    
    # Export to KITTI format
    kitti_export_dir = os.path.join(PROJECT_ROOT, "kitti_dataset")
    if os.path.exists(kitti_export_dir):
        shutil.rmtree(kitti_export_dir)
    
    print(f"📁 Exporting to KITTI format: {kitti_export_dir}")
    dataset.export(
        export_dir=kitti_export_dir,
        dataset_type=fo.types.KITTIDetectionDataset,
        label_field=LABEL_FIELD
    )
    
    print(f"\n🎉 --- EXPORT COMPLETE ---")
    print(f"✅ YOLO dataset: {yolo_export_dir}")
    print(f"✅ KITTI dataset: {kitti_export_dir}")
    print(f"📊 Total annotations: {total_annotations}")

def main():
    """Main pipeline for image auto-labeling and annotation."""
    print("🚀 YOLO-World Image Auto-Labeling Pipeline")
    print("=" * 50)
    
    # Check if images directory exists
    if not create_images_directory():
        return
    
    # Get image paths
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_paths = [
        os.path.join(IMAGES_DIR, f) 
        for f in os.listdir(IMAGES_DIR)
        if os.path.splitext(f.lower())[1] in image_extensions
    ]
    
    if not image_paths:
        print(f"❌ No images found in {IMAGES_DIR}")
        print("Please add images to the directory and run again.")
        return
    
    print(f"📸 Found {len(image_paths)} images")
    
    # Initialize dataset
    dataset = init_dataset(image_paths)
    
    # Auto-label with YOLO-World
    dataset = auto_label_with_yolo_world(dataset)
    
    # Launch annotation interface
    launch_annotation_interface(dataset)
    
    # Export final datasets
    export_datasets(dataset)
    
    print("\n🚀 Ready for model training and evaluation!")

if __name__ == "__main__":
    main()
