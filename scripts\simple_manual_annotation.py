import os
import shutil
import fiftyone as fo
from ultralytics import <PERSON><PERSON><PERSON>
from datetime import datetime

# =============================================================================
# SIMPLE MANUAL ANNOTATION SCRIPT (Fixed Version)
#
# Author: Dr<PERSON> <PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Purpose: Create perfect ground truth with simple, working setup
# =============================================================================

# ---
# Configuration
# ---
CLIP_NAME = "clip_005"
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
CLIP_PATH = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical", f"{CLIP_NAME}.mp4")
DATASET_NAME = f"cts-perfect-gt-{CLIP_NAME}"
SUGGESTIONS_FIELD = "suggestions"
GROUND_TRUTH_FIELD = "ground_truth"
MODEL_CONFIG = "yolov8l-worldv2.pt"

# Detection classes (your exact CVAT setup)
DETECTION_CLASSES = [
    "car", "truck", "bus", "motorcycle", 
    "person", "traffic light", "stop sign", "DontCare"
]

# YOLO-World classes (subset for detection)
YOLO_CLASSES = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# Settings
SUGGESTION_CONFIDENCE = 0.2
SAMPLE_FRAMES = 50  # Only annotate 50 frames for testing

def sample_frames_evenly(dataset, num_samples=50):
    """Sample frames evenly distributed across the video"""
    print(f"📊 Sampling {num_samples} frames evenly from the video...")

    for sample in dataset:
        if hasattr(sample, 'frames'):
            total_frames = len(sample.frames)
            frame_numbers = list(sample.frames.keys())

            if total_frames <= num_samples:
                # If video has fewer frames than requested, keep all
                print(f"📹 Video has {total_frames} frames - keeping all")
                return dataset

            # Calculate step size for even sampling
            step = total_frames // num_samples
            sampled_frame_numbers = []

            for i in range(num_samples):
                frame_idx = i * step
                if frame_idx < len(frame_numbers):
                    sampled_frame_numbers.append(frame_numbers[frame_idx])

            # Create new frames dict with only sampled frames
            sampled_frames = {}
            for frame_num in sampled_frame_numbers:
                if frame_num in sample.frames:
                    sampled_frames[frame_num] = sample.frames[frame_num]

            # Replace frames with sampled subset
            sample.frames = sampled_frames

            print(f"📊 Sampled {len(sampled_frames)} frames from {total_frames} total frames")
            print(f"📋 Frame numbers: {sorted(sampled_frame_numbers)[:10]}{'...' if len(sampled_frame_numbers) > 10 else ''}")

    return dataset

def create_initial_suggestions(dataset):
    """Generate YOLO-World suggestions as starting point"""
    print(f"🤖 Generating initial suggestions with YOLO-World...")
    
    model = YOLO(MODEL_CONFIG)
    model.set_classes(YOLO_CLASSES)
    print(f"🎯 Detection classes: {YOLO_CLASSES}")
    
    # Apply model with low confidence for more suggestions
    print(f"🔍 Running suggestion generation (confidence >= {SUGGESTION_CONFIDENCE})...")
    dataset.apply_model(
        model, 
        label_field=SUGGESTIONS_FIELD,
        confidence_thresh=SUGGESTION_CONFIDENCE
    )
    
    # Count suggestions
    total_suggestions = 0
    for sample in dataset:
        if hasattr(sample, 'frames'):
            for frame in sample.frames.values():
                if hasattr(frame, SUGGESTIONS_FIELD) and getattr(frame, SUGGESTIONS_FIELD):
                    total_suggestions += len(getattr(frame, SUGGESTIONS_FIELD).detections)
    
    print(f"✅ Generated {total_suggestions} suggestions across all frames")
    return dataset

def setup_manual_annotation(dataset):
    """Set up manual annotation environment"""
    print(f"\n🎯 Setting up manual annotation environment...")
    
    # Clone suggestions to ground truth field
    if SUGGESTIONS_FIELD in dataset.get_frame_field_schema():
        print(f"📋 Cloning suggestions to '{GROUND_TRUTH_FIELD}' field")
        dataset.clone_frame_field(SUGGESTIONS_FIELD, GROUND_TRUTH_FIELD)
    else:
        print(f"📝 Creating empty '{GROUND_TRUTH_FIELD}' field")
        dataset.add_frame_field(GROUND_TRUTH_FIELD, fo.EmbeddedDocumentField, embedded_doc_type=fo.Detections)
    
    # Launch FiftyOne App
    print(f"📊 Launching FiftyOne App...")
    session = fo.launch_app(dataset, auto=False)
    
    # Simple label schema without complex attributes
    label_schema = {
        f"frames.{GROUND_TRUTH_FIELD}": {
            "type": "detections",
            "classes": DETECTION_CLASSES
        }
    }
    
    # Launch CVAT (use valid variable name for annotation key)
    task_name = f"perfect_gt_{CLIP_NAME}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"🚀 Launching CVAT annotation task: {task_name}")
    
    dataset.annotate(
        task_name,
        label_schema=label_schema,
        launch_editor=True
    )
    
    return session

def export_datasets(dataset):
    """Export the annotated dataset"""
    print("\n📦 --- AUTOMATED EXPORT PIPELINE ---")
    
    # Count final annotations
    final_annotation_count = 0
    for sample in dataset:
        if hasattr(sample, 'frames'):
            for frame in sample.frames.values():
                if hasattr(frame, GROUND_TRUTH_FIELD) and getattr(frame, GROUND_TRUTH_FIELD):
                    final_annotation_count += len(getattr(frame, GROUND_TRUTH_FIELD).detections)
    
    print(f"📊 Perfect ground truth contains {final_annotation_count} manually verified annotations")
    
    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Export to YOLO format
    yolo_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_YOLO_Dataset_{timestamp}")
    print(f"🎯 Exporting to YOLO format: {yolo_export_dir}")
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}",
        classes=DETECTION_CLASSES,
        split=0.8
    )

    # Export to COCO format
    coco_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_COCO_Dataset_{timestamp}")
    print(f"📁 Exporting to COCO format: {coco_export_dir}")
    dataset.export(
        export_dir=coco_export_dir,
        dataset_type=fo.types.COCODetectionDataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}"
    )

    print("\n🎉 --- PERFECT GROUND TRUTH COMPLETE ---")
    print(f"✅ FiftyOne dataset: '{DATASET_NAME}'")
    print(f"✅ YOLO format: {yolo_export_dir}")
    print(f"✅ COCO format: {coco_export_dir}")
    print(f"📊 Total perfect annotations: {final_annotation_count}")
    print("\n🚀 Ready for your dual-stream pipeline!")

def main():
    """Simple manual annotation pipeline"""
    
    print("🎯 SIMPLE MANUAL ANNOTATION PIPELINE (TESTING MODE)")
    print("=" * 60)
    print(f"📹 Video: {CLIP_PATH}")
    print(f"🎯 Goal: 100% manually verified ground truth")
    print(f"🧪 Testing with {SAMPLE_FRAMES} sampled frames")
    print("=" * 60)

    # Step 1: Dataset Setup
    if fo.dataset_exists(DATASET_NAME):
        print(f"🗑️  Deleting existing dataset '{DATASET_NAME}'")
        fo.delete_dataset(DATASET_NAME)

    print(f"📹 Loading video into FiftyOne dataset...")
    dataset = fo.Dataset.from_videos([CLIP_PATH])
    dataset.name = DATASET_NAME
    dataset.persistent = True

    # Count total frames
    total_frames = sum(len(sample.frames) for sample in dataset)
    print(f"📊 Total frames in video: {total_frames}")

    # Step 2: Sample frames for testing
    dataset = sample_frames_evenly(dataset, SAMPLE_FRAMES)

    # Count sampled frames
    sampled_frames = sum(len(sample.frames) for sample in dataset)
    print(f"🎯 Frames to annotate: {sampled_frames}")

    # Step 3: Generate suggestions
    dataset = create_initial_suggestions(dataset)

    # Step 4: Setup annotation
    session = setup_manual_annotation(dataset)

    # Step 5: Instructions
    print("\n" + "=" * 60)
    print("🎯 MANUAL ANNOTATION READY! (TESTING MODE)")
    print("=" * 60)
    print("🌐 CVAT is open in your browser")
    print("📊 FiftyOne app is monitoring your progress")
    print("")
    print("📝 YOUR TASK:")
    print(f"   • Review {sampled_frames} sampled frames manually")
    print("   • Fix/verify ALL bounding boxes")
    print("   • Add 'DontCare' for ambiguous objects")
    print("   • Ensure 100% perfect ground truth")
    print("")
    print("💡 TIPS:")
    print("   • Use keyboard shortcuts in CVAT for speed")
    print("   • Check FiftyOne app to monitor progress")
    print("   • This is a test - if it works, we'll scale to all frames!")
    print("")
    print("🚀 When finished: Close FiftyOne app window to trigger export")
    print("=" * 60)
    
    # Wait for completion
    session.wait()

    # Step 6: Export
    export_datasets(dataset)

if __name__ == "__main__":
    main()
