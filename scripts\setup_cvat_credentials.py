import os
import getpass

def setup_cvat_credentials():
    """Set up CVAT credentials as environment variables"""
    print("🔐 CVAT Credentials Setup")
    print("=" * 40)
    print("This will set up your CVAT credentials for FiftyOne integration")
    print("Your credentials will be stored as environment variables")
    print("")
    
    # Get credentials
    username = input("Enter your CVAT username: ")
    password = getpass.getpass("Enter your CVAT password: ")
    email = input("Enter your CVAT email (optional): ")
    
    # Set environment variables for current session
    os.environ['FIFTYONE_CVAT_USERNAME'] = username
    os.environ['FIFTYONE_CVAT_PASSWORD'] = password
    if email:
        os.environ['FIFTYONE_CVAT_EMAIL'] = email
    
    print("\n✅ Credentials set for current session!")
    print("\n📝 To make these permanent, add to your system environment:")
    print(f"   FIFTYONE_CVAT_USERNAME={username}")
    print(f"   FIFTYONE_CVAT_PASSWORD=<your_password>")
    if email:
        print(f"   FIFTYONE_CVAT_EMAIL={email}")
    
    print("\n🚀 You can now run the CVAT annotation script!")
    
    return username, password, email

if __name__ == "__main__":
    setup_cvat_credentials()
