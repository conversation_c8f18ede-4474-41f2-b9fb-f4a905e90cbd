# 🤖 YOLO-World Auto-Labeling Pipeline

This guide explains how to use the enhanced YOLO-World auto-labeling pipeline for efficient annotation of traffic scenes.

## 🎯 Overview

The pipeline combines **YOLO-World** (zero-shot detection) with **CVAT** (manual correction) to create high-quality annotated datasets:

1. **Auto-label** with YOLO-World to generate initial bounding boxes
2. **Review & correct** labels in CVAT interface  
3. **Export** clean datasets in YOLO/KITTI formats for training

## 📋 Prerequisites

Ensure you have the required dependencies:

```bash
pip install fiftyone ultralytics opencv-python pandas numpy
```

## 🚀 Quick Start

### Option 1: Video-Based Annotation (Recommended)

Use the enhanced master workflow for video clips:

```bash
python scripts/master_annotation_workflow.py
```

**Features:**
- ✅ Auto-labels entire video with YOLO-World
- ✅ Frame-by-frame correction in CVAT
- ✅ Vehicle attributes (braking, turn signals)
- ✅ Exports to YOLO & KITTI formats

### Option 2: Image-Based Annotation

For image datasets or extracted frames:

1. **Extract frames from videos** (optional):
   ```bash
   python scripts/extract_frames.py
   ```

2. **Run image annotation pipeline**:
   ```bash
   python scripts/yolo_world_image_annotator.py
   ```

## 🔧 Configuration

### Detection Classes

The pipeline detects these classes automatically:
- `car`, `truck`, `bus`, `motorcycle`
- `person`, `traffic light`, `stop sign`

Plus manual addition of:
- `DontCare` (for ambiguous objects)

### Vehicle Attributes

For vehicles, you can annotate:
- `is_braking` (brake lights visible)
- `turn_signal` (left/right indicators)
- `hazard_lights` (emergency flashers)

### YOLO-World Settings

```python
CONFIDENCE_THRESHOLD = 0.3  # Lower = more detections to review
IOU_THRESHOLD = 0.5         # Non-max suppression
MAX_DETECTIONS = 300        # Max objects per frame
```

## 📁 Directory Structure

```
CTS_Thesis_Project/
├── scripts/
│   ├── master_annotation_workflow.py    # Enhanced video workflow
│   ├── yolo_world_image_annotator.py   # Image annotation pipeline
│   └── extract_frames.py               # Frame extraction utility
├── 3_Extracted_Clips/
│   ├── critical/                       # High-priority clips
│   └── normal/                         # Regular clips
├── images/                             # Extracted frames (auto-created)
├── YOLO_Training_Dataset/              # Final YOLO export
├── KITTI_Dataset/                      # Final KITTI export
└── yolov8l-worldv2.pt                 # YOLO-World model
```

## 🎬 Workflow Steps

### 1. Auto-Labeling Phase
- YOLO-World scans all frames
- Generates initial bounding boxes
- Creates `predictions` field in FiftyOne

### 2. Manual Correction Phase  
- CVAT launches in browser
- Review auto-generated labels
- Fix incorrect boxes
- Add missing objects
- Set vehicle attributes
- Add `DontCare` for ambiguous cases

### 3. Export Phase
- **YOLO format**: Ready for YOLOv8/v11/v12 training
- **KITTI format**: Compatible with research tools
- Includes train/validation splits

## 🛠️ Customization

### Modify Detection Classes

Edit the class lists in the script:

```python
AUTO_LABEL_PROMPTS = ["car", "truck", "bus", "motorcycle", "person"]
ALL_CLASSES = AUTO_LABEL_PROMPTS + ["DontCare", "bicycle"]
```

### Adjust Confidence Threshold

Lower threshold = more detections to review:

```python
CONFIDENCE_THRESHOLD = 0.2  # More sensitive
CONFIDENCE_THRESHOLD = 0.5  # More selective
```

### Add Custom Attributes

```python
ATTRIBUTES = {
    "car": ["is_braking", "turn_signal", "door_open"],
    "person": ["is_pedestrian", "is_cyclist"]
}
```

## 📊 Quality Control

### Review Auto-Labels
- Check for false positives (incorrect detections)
- Look for false negatives (missed objects)
- Verify bounding box accuracy

### Best Practices
- Use `DontCare` for partially visible/ambiguous objects
- Ensure consistent labeling across frames
- Set attributes only when clearly visible

## 🚀 Next Steps

After annotation:

1. **Train custom models**:
   ```bash
   yolo train data=YOLO_Training_Dataset/dataset.yaml model=yolov8n.pt epochs=100
   ```

2. **Evaluate performance**:
   - Compare predictions vs ground truth
   - Calculate mAP, precision, recall
   - Analyze per-class performance

3. **Iterate**:
   - Add more training data
   - Adjust class definitions
   - Fine-tune model parameters

## 🔍 Troubleshooting

### Common Issues

**CVAT won't launch:**
- Check FiftyOne installation: `fiftyone --version`
- Ensure browser allows popups
- Try: `fo.launch_app()` manually

**No auto-detections:**
- Lower confidence threshold
- Check YOLO-World model path
- Verify video/image quality

**Export errors:**
- Ensure ground truth field exists
- Check class name consistency
- Verify output directory permissions

### Performance Tips

- Use GPU for faster inference: `device='cuda'`
- Process shorter clips for testing
- Adjust frame extraction interval

## 📞 Support

For issues or questions:
1. Check FiftyOne docs: https://docs.voxel51.com/
2. YOLO-World docs: https://docs.ultralytics.com/models/yolo-world/
3. Review script comments and error messages

---

**Happy Annotating! 🎯**
