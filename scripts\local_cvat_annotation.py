import os
import shutil
import fiftyone as fo
from ultralytics import <PERSON><PERSON><PERSON>
from datetime import datetime
import cv2
import tempfile

# =============================================================================
# LOCAL CVAT ANNOTATION SCRIPT
#
# Author: Dr<PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Purpose: Create perfect ground truth using LOCAL CVAT installation
# =============================================================================

# ---
# Configuration
# ---
CLIP_NAME = "clip_005"
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
CLIP_PATH = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical", f"{CLIP_NAME}.mp4")
DATASET_NAME = f"cts-perfect-gt-{CLIP_NAME}"
SUGGESTIONS_FIELD = "suggestions"
GROUND_TRUTH_FIELD = "ground_truth"
MODEL_CONFIG = "yolov8l-worldv2.pt"

# Local CVAT Configuration
LOCAL_CVAT_URL = "http://localhost:8080"  # Default local CVAT URL
LOCAL_CVAT_USERNAME = "admin"  # Default local CVAT admin
LOCAL_CVAT_PASSWORD = "admin"  # Default local CVAT password

# Detection classes (matching your CVAT project)
DETECTION_CLASSES = [
    "car", "truck", "bus", "motorcycle", 
    "person", "traffic light", "stop sign", "DontCare"
]

# YOLO-World classes (subset for detection)
YOLO_CLASSES = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# Settings
SUGGESTION_CONFIDENCE = 0.2
SAMPLE_FRAMES = 50  # Only annotate 50 frames for testing

def setup_local_cvat_config():
    """Configure FiftyOne to use local CVAT"""
    print(f"🔧 Configuring FiftyOne for local CVAT at {LOCAL_CVAT_URL}")
    
    # Set environment variables for local CVAT
    os.environ['FIFTYONE_CVAT_URL'] = LOCAL_CVAT_URL
    os.environ['FIFTYONE_CVAT_USERNAME'] = LOCAL_CVAT_USERNAME
    os.environ['FIFTYONE_CVAT_PASSWORD'] = LOCAL_CVAT_PASSWORD
    
    print(f"✅ Local CVAT configured:")
    print(f"   URL: {LOCAL_CVAT_URL}")
    print(f"   Username: {LOCAL_CVAT_USERNAME}")

def create_sampled_dataset(video_path, num_samples=50):
    """Create dataset with only sampled frames to avoid processing all frames"""
    print(f"📊 Creating dataset with {num_samples} evenly sampled frames...")
    
    # Load video to get frame count
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    cap.release()
    
    print(f"📹 Video has {total_frames} total frames at {fps:.2f} FPS")
    
    if total_frames <= num_samples:
        print(f"📹 Video has fewer frames than requested - using all {total_frames} frames")
        # Use all frames
        dataset = fo.Dataset.from_videos([video_path])
        return dataset, total_frames
    
    # Calculate which frames to sample
    step = total_frames // num_samples
    sampled_frame_indices = []
    
    for i in range(num_samples):
        frame_idx = i * step
        if frame_idx < total_frames:
            sampled_frame_indices.append(frame_idx)
    
    print(f"📋 Sampling frames: {sampled_frame_indices[:10]}{'...' if len(sampled_frame_indices) > 10 else ''}")
    
    # Create dataset with only sampled frames
    dataset = fo.Dataset()
    
    # Extract sampled frames and create samples
    cap = cv2.VideoCapture(video_path)
    
    # Create temporary directory for sampled frames
    temp_dir = tempfile.mkdtemp()
    frame_paths = []
    
    for i, frame_idx in enumerate(sampled_frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if ret:
            # Save frame as image
            frame_path = os.path.join(temp_dir, f"frame_{frame_idx:06d}.jpg")
            cv2.imwrite(frame_path, frame)
            frame_paths.append(frame_path)
    
    cap.release()
    
    # Add frames as image samples to dataset
    samples = []
    for frame_path in frame_paths:
        sample = fo.Sample(filepath=frame_path)
        samples.append(sample)
    
    dataset.add_samples(samples)
    
    print(f"✅ Created dataset with {len(samples)} sampled frames")
    return dataset, len(samples)

def create_initial_suggestions(dataset):
    """Generate YOLO-World suggestions as starting point"""
    print(f"🤖 Generating initial suggestions with YOLO-World...")
    
    model = YOLO(MODEL_CONFIG)
    model.set_classes(YOLO_CLASSES)
    print(f"🎯 Detection classes: {YOLO_CLASSES}")
    
    # Apply model with low confidence for more suggestions
    print(f"🔍 Running suggestion generation (confidence >= {SUGGESTION_CONFIDENCE})...")
    dataset.apply_model(
        model, 
        label_field=SUGGESTIONS_FIELD,
        confidence_thresh=SUGGESTION_CONFIDENCE
    )
    
    # Count suggestions (for image samples, not video frames)
    total_suggestions = 0
    for sample in dataset:
        if hasattr(sample, SUGGESTIONS_FIELD) and getattr(sample, SUGGESTIONS_FIELD):
            total_suggestions += len(getattr(sample, SUGGESTIONS_FIELD).detections)
    
    print(f"✅ Generated {total_suggestions} suggestions across {len(dataset)} sampled frames")
    return dataset

def setup_local_cvat_annotation(dataset):
    """Set up local CVAT annotation"""
    print(f"\n🎯 Setting up LOCAL CVAT annotation environment...")
    
    # Clone suggestions to ground truth field (for image samples)
    if SUGGESTIONS_FIELD in dataset.get_field_schema():
        print(f"📋 Cloning suggestions to '{GROUND_TRUTH_FIELD}' field")
        dataset.clone_sample_field(SUGGESTIONS_FIELD, GROUND_TRUTH_FIELD)
    else:
        print(f"📝 Creating empty '{GROUND_TRUTH_FIELD}' field")
        dataset.add_sample_field(GROUND_TRUTH_FIELD, fo.EmbeddedDocumentField, embedded_doc_type=fo.Detections)
    
    # Launch FiftyOne App
    print(f"📊 Launching FiftyOne App...")
    session = fo.launch_app(dataset, auto=False)
    
    # Simple label schema for local CVAT
    label_schema = {
        GROUND_TRUTH_FIELD: {
            "type": "detections",
            "classes": DETECTION_CLASSES
        }
    }
    
    # Create CVAT task with proper naming
    task_name = f"cts_local_{CLIP_NAME}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"🚀 Creating LOCAL CVAT annotation task: {task_name}")
    print(f"🌐 Connecting to: {LOCAL_CVAT_URL}")
    
    try:
        # Try to annotate with local CVAT
        dataset.annotate(
            task_name,
            label_schema=label_schema,
            launch_editor=True
        )
        print("✅ Local CVAT task created successfully!")
        print(f"🌐 CVAT should open at: {LOCAL_CVAT_URL}")
        
    except Exception as e:
        print(f"❌ Local CVAT connection failed: {e}")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Make sure local CVAT is running:")
        print("   docker-compose up -d")
        print("2. Check CVAT is accessible at http://localhost:8080")
        print("3. Verify admin credentials are set up")
        print("4. Check firewall/port settings")
        print("\n📊 FiftyOne app is still running for reference")
    
    return session

def export_datasets(dataset):
    """Export the annotated dataset"""
    print("\n📦 --- AUTOMATED EXPORT PIPELINE ---")
    
    # Count final annotations (for image samples)
    final_annotation_count = 0
    for sample in dataset:
        if hasattr(sample, GROUND_TRUTH_FIELD) and getattr(sample, GROUND_TRUTH_FIELD):
            final_annotation_count += len(getattr(sample, GROUND_TRUTH_FIELD).detections)
    
    print(f"📊 Perfect ground truth contains {final_annotation_count} manually verified annotations")
    
    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Export to YOLO format
    yolo_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_YOLO_Dataset_{timestamp}")
    print(f"🎯 Exporting to YOLO format: {yolo_export_dir}")
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=GROUND_TRUTH_FIELD,
        classes=DETECTION_CLASSES,
        split=0.8
    )

    # Export to COCO format
    coco_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_COCO_Dataset_{timestamp}")
    print(f"📁 Exporting to COCO format: {coco_export_dir}")
    dataset.export(
        export_dir=coco_export_dir,
        dataset_type=fo.types.COCODetectionDataset,
        label_field=GROUND_TRUTH_FIELD
    )

    print("\n🎉 --- PERFECT GROUND TRUTH COMPLETE ---")
    print(f"✅ FiftyOne dataset: '{DATASET_NAME}'")
    print(f"✅ YOLO format: {yolo_export_dir}")
    print(f"✅ COCO format: {coco_export_dir}")
    print(f"📊 Total perfect annotations: {final_annotation_count}")
    print("\n🚀 Ready for your dual-stream pipeline!")

def main():
    """Local CVAT annotation pipeline"""
    
    print("🎯 LOCAL CVAT ANNOTATION PIPELINE (TESTING MODE)")
    print("=" * 60)
    print(f"📹 Video: {CLIP_PATH}")
    print(f"🎯 Goal: 100% manually verified ground truth")
    print(f"🧪 Testing with {SAMPLE_FRAMES} sampled frames")
    print(f"🔧 Using LOCAL CVAT installation")
    print("=" * 60)

    # Step 0: Configure local CVAT
    setup_local_cvat_config()

    # Step 1: Dataset Setup with Frame Sampling
    if fo.dataset_exists(DATASET_NAME):
        print(f"🗑️  Deleting existing dataset '{DATASET_NAME}'")
        fo.delete_dataset(DATASET_NAME)

    # Create dataset with only sampled frames (much faster!)
    dataset, sampled_frames = create_sampled_dataset(CLIP_PATH, SAMPLE_FRAMES)
    dataset.name = DATASET_NAME
    dataset.persistent = True
    
    print(f"🎯 Frames to annotate: {sampled_frames}")

    # Step 2: Generate suggestions (only on sampled frames)
    dataset = create_initial_suggestions(dataset)

    # Step 3: Setup local CVAT annotation
    session = setup_local_cvat_annotation(dataset)

    # Step 4: Instructions
    print("\n" + "=" * 60)
    print("🎯 LOCAL CVAT ANNOTATION READY! (TESTING MODE)")
    print("=" * 60)
    print("🌐 FiftyOne app is open for reference")
    print(f"🔧 Local CVAT should open at: {LOCAL_CVAT_URL}")
    print("")
    print("📝 YOUR TASK:")
    print(f"   • Annotate {sampled_frames} sampled frames in LOCAL CVAT")
    print("   • Fix/verify ALL bounding boxes")
    print("   • Add 'DontCare' for ambiguous objects")
    print("   • Ensure 100% perfect ground truth")
    print("")
    print("💡 LOCAL CVAT TIPS:")
    print("   • Use keyboard shortcuts for speed")
    print("   • Save frequently in CVAT")
    print("   • Local CVAT = faster, more reliable")
    print("   • This is a test - if it works, we'll scale to all frames!")
    print("")
    print("🚀 When finished: Close FiftyOne app window to trigger export")
    print("=" * 60)
    
    # Wait for completion
    session.wait()

    # Step 4: Export
    export_datasets(dataset)

if __name__ == "__main__":
    main()
