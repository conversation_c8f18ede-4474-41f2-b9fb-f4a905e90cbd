import os
import shutil
import fiftyone as fo
from fiftyone import <PERSON><PERSON><PERSON> as F
from ultralytics import YOLO

# =============================================================================
# MASTER ANNOTATION & DATASET CREATION SCRIPT (v1.3 - Fixed Frame Field Issue)
#
# Author: Dr. <PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Change Log (v1.3):
# - Fixed the dataset.annotate() call to use proper frame-level field format
# - Label field now correctly uses "frames.ground_truth" for video datasets
# =============================================================================

# ---
# Stage 1: Configuration
# ---
CLIP_NAME = "clip_005"
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
CLIP_PATH = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical", f"{CLIP_NAME}.mp4")
DATASET_NAME = f"cts-master-{CLIP_NAME}"
PREDICTIONS_FIELD = "predictions"
GROUND_TRUTH_FIELD = "ground_truth"
MODEL_CONFIG = "yolov8l-worldv2.pt"

# --- CORRECTED CLASS HANDLING ---
# List of objects for the YOLO-World model to actively search for.
# 'DontCare' is intentionally excluded.
CLASSES_TO_DETECT = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# The complete list of all possible labels for our final dataset, including
# those added by a human annotator. This list is used to configure CVAT and the final export.
ALL_CLASSES_FOR_ANNOTATION = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign", "DontCare"]
# --- END CORRECTION ---

CONFIDENCE_THRESHOLD = 0.4

# ---
# Stage 2: Main Workflow Logic
# ---
def main():
    """Main function to execute the annotation and dataset creation pipeline."""

    # --- 2.1: Dataset Setup ---
    if fo.dataset_exists(DATASET_NAME):
        print(f"Found existing dataset '{DATASET_NAME}'. Deleting for a fresh start.")
        fo.delete_dataset(DATASET_NAME)

    print(f"Loading video from '{CLIP_PATH}' into new dataset '{DATASET_NAME}'.")
    dataset = fo.Dataset.from_videos([CLIP_PATH])
    dataset.name = DATASET_NAME
    dataset.persistent = True

    # --- 2.2: Auto-Labeling with YOLO-World ---
    print(f"Applying model '{MODEL_CONFIG}' to generate initial predictions...")
    model = YOLO(MODEL_CONFIG)
    model.set_classes(CLASSES_TO_DETECT) 

    dataset.apply_model(model, label_field=PREDICTIONS_FIELD)
    print("Auto-labeling complete.")

    # --- 2.3: Human-in-the-Loop Correction via CVAT ---
    print("\n--- ACTION REQUIRED: Manual Annotation in CVAT ---")
    
    # --- ROBUSTNESS FIX v1.4 ---
    # This logic now correctly handles both cases:
    # 1. If predictions exist, clone them to create the ground_truth field.
    # 2. If no predictions exist, create a new, empty ground_truth field.
    if PREDICTIONS_FIELD in dataset.get_frame_field_schema():
        print(f"Cloning initial predictions to '{GROUND_TRUTH_FIELD}' field for correction.")
        dataset.clone_frame_field(PREDICTIONS_FIELD, GROUND_TRUTH_FIELD)
    else:
        print("WARNING: Model found no objects. Creating an empty 'ground_truth' field for manual annotation.")
        dataset.add_frame_field(GROUND_TRUTH_FIELD, fo.EmbeddedDocumentField, embedded_doc_type=fo.Detections)
    # --- END FIX ---
    
    session = fo.launch_app(dataset, auto=False)
    
    # FIXED: Use proper frame-level field format for video datasets
    dataset.annotate(
        f"cts‑correction‑task‑{CLIP_NAME}",
        label_field=f"frames.{GROUND_TRUTH_FIELD}",  # Fixed: Added "frames." prefix
        label_type="detections",
        classes=ALL_CLASSES_FOR_ANNOTATION,
        launch_editor=True
    )

    
    print("CVAT has been launched in your browser.")
    print("Please correct the labels in the 'ground_truth' field.")
    print(">>> When finished, click 'Refresh' in the FiftyOne App, then CLOSE THE APP WINDOW to continue.")
    
    session.wait()

    # --- 2.4: Final Dataset Export ---
    print("\n--- Annotation complete. Exporting the final dataset... ---")
    
    # --- OPTION A: Export to KITTI Format ---
    kitti_export_dir = os.path.join(PROJECT_ROOT, "KITTI_Dataset", CLIP_NAME)
    if os.path.exists(kitti_export_dir):
        shutil.rmtree(kitti_export_dir)
        
    print(f"Exporting to KITTI format at: '{kitti_export_dir}'")
    dataset.export(
        export_dir=kitti_export_dir,
        dataset_type=fo.types.KITTIDetectionDataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}"
    )

    # --- OPTION B: Export to YOLO Format ---
    yolo_export_dir = os.path.join(PROJECT_ROOT, "YOLO_Training_Dataset")
    if os.path.exists(yolo_export_dir):
        shutil.rmtree(yolo_export_dir)

    print(f"Exporting to YOLO format at: '{yolo_export_dir}'")
    dataset.tags = ["train"]
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}",
        classes=ALL_CLASSES_FOR_ANNOTATION,
        split_tags="tags"
    )

    print("\n--- WORKFLOW COMPLETE ---")
    print("Your master dataset is saved in FiftyOne, and exported versions are ready.")

if __name__ == "__main__":
    main()