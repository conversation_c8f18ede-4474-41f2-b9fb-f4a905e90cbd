import os
import shutil
import fiftyone as fo
from fiftyone import <PERSON><PERSON>ield as F
from ultralytics import YOLO
import json
import threading
import time
from datetime import datetime
from pathlib import Path

# =============================================================================
# PERFECT GROUND TRUTH ANNOTATION SCRIPT (v3.0 - Manual Perfection)
#
# Author: Dr. <PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Purpose: Create perfect ground truth dataset with 100% manual verification
# Philosophy: Human does the annotation, script handles everything else
# =============================================================================

# ---
# Configuration for Perfect Ground Truth Creation
# ---
CLIP_NAME = "clip_005"
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
CLIP_PATH = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical", f"{CLIP_NAME}.mp4")
DATASET_NAME = f"cts-perfect-gt-{CLIP_NAME}"
SUGGESTIONS_FIELD = "suggestions"  # YOLO-World suggestions (starting point)
GROUND_TRUTH_FIELD = "ground_truth"  # Your perfect manual annotations
MODEL_CONFIG = "yolov8l-worldv2.pt"

# --- ANNOTATION CLASSES (Your Exact CVAT Setup) ---
DETECTION_CLASSES = [
    "car", "truck", "bus", "motorcycle",
    "person", "traffic light", "stop sign", "DontCare"
]

# --- VEHICLE ATTRIBUTES (Your Exact CVAT Setup) ---
VEHICLE_ATTRIBUTES = {
    "car": {
        "is_braking": {"type": "select", "values": ["true", "false"], "default": "false"},
        "turn_signal": {"type": "select", "values": ["none", "left", "right", "hazard"], "default": "none"}
    },
    "truck": {
        "is_braking": {"type": "select", "values": ["true", "false"], "default": "false"},
        "turn_signal": {"type": "select", "values": ["none", "left", "right", "hazard"], "default": "none"}
    },
    "bus": {
        "is_braking": {"type": "select", "values": ["true", "false"], "default": "false"},
        "turn_signal": {"type": "select", "values": ["none", "left", "right", "hazard"], "default": "none"}
    },
    "motorcycle": {
        "is_braking": {"type": "select", "values": ["true", "false"], "default": "false"},
        "turn_signal": {"type": "select", "values": ["none", "left", "right", "hazard"], "default": "none"}
    }
}

# --- YOLO-World SETTINGS (Just for initial suggestions) ---
SUGGESTION_CONFIDENCE = 0.2  # Low threshold = more suggestions to review
YOLO_CLASSES = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# ---
# Core Functions for Perfect Ground Truth Creation
# ---
def create_initial_suggestions(dataset):
    """
    Generate YOLO-World suggestions as starting point for manual annotation.
    These are just suggestions - you'll verify every single detection.
    """
    print(f"🤖 Generating initial suggestions with YOLO-World...")
    print(f"📋 Model: {MODEL_CONFIG}")

    model = YOLO(MODEL_CONFIG)
    model.set_classes(YOLO_CLASSES)
    print(f"🎯 Detection classes: {YOLO_CLASSES}")

    # Apply model with low confidence for more suggestions
    print(f"🔍 Running suggestion generation (confidence >= {SUGGESTION_CONFIDENCE})...")
    dataset.apply_model(
        model,
        label_field=SUGGESTIONS_FIELD,
        confidence_thresh=SUGGESTION_CONFIDENCE,
        iou_thresh=0.5,
        max_dets=300
    )

    # Count suggestions
    total_suggestions = 0
    for sample in dataset:
        if hasattr(sample, 'frames'):
            for frame in sample.frames.values():
                if hasattr(frame, SUGGESTIONS_FIELD) and getattr(frame, SUGGESTIONS_FIELD):
                    total_suggestions += len(getattr(frame, SUGGESTIONS_FIELD).detections)

    print(f"✅ Generated {total_suggestions} suggestions across all frames")
    print(f"📝 You'll manually verify every single detection for perfect ground truth")

    return dataset

def setup_manual_annotation_environment(dataset):
    """
    Set up the perfect environment for manual annotation.
    """
    print(f"\n🎯 Setting up manual annotation environment...")

    # Clone suggestions to ground truth field for editing
    if SUGGESTIONS_FIELD in dataset.get_frame_field_schema():
        print(f"📋 Cloning suggestions to '{GROUND_TRUTH_FIELD}' field for manual editing")
        dataset.clone_frame_field(SUGGESTIONS_FIELD, GROUND_TRUTH_FIELD)
    else:
        print(f"📝 Creating empty '{GROUND_TRUTH_FIELD}' field for manual annotation")
        dataset.add_frame_field(GROUND_TRUTH_FIELD, fo.EmbeddedDocumentField, embedded_doc_type=fo.Detections)

    # Launch FiftyOne App for progress monitoring
    print(f"📊 Launching FiftyOne App for progress monitoring...")
    session = fo.launch_app(dataset, auto=False)

    # Create enhanced label schema matching your CVAT setup
    label_schema = {
        f"frames.{GROUND_TRUTH_FIELD}": {
            "type": "detections",
            "classes": DETECTION_CLASSES,
            "attributes": VEHICLE_ATTRIBUTES
        }
    }

    # Launch CVAT for manual annotation
    task_name = f"perfect-gt-{CLIP_NAME}-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"🚀 Launching CVAT annotation task: {task_name}")

    dataset.annotate(
        task_name,
        label_schema=label_schema,
        launch_editor=True
    )

    return session, task_name

def main():
    """
    Perfect Ground Truth Creation Pipeline
    - Script handles all logistics
    - You focus on perfect annotation quality
    """

    print("🎯 PERFECT GROUND TRUTH CREATION PIPELINE")
    print("=" * 60)
    print(f"📹 Video: {CLIP_PATH}")
    print(f"🎯 Goal: 100% manually verified ground truth")
    print("=" * 60)

    # --- Step 1: Dataset Setup ---
    if fo.dataset_exists(DATASET_NAME):
        print(f"🗑️  Deleting existing dataset '{DATASET_NAME}' for fresh start")
        fo.delete_dataset(DATASET_NAME)

    print(f"📹 Loading video into FiftyOne dataset...")
    dataset = fo.Dataset.from_videos([CLIP_PATH])
    dataset.name = DATASET_NAME
    dataset.persistent = True

    # Count total frames
    total_frames = sum(len(sample.frames) for sample in dataset)
    print(f"📊 Total frames to annotate: {total_frames}")

    # --- Step 2: Generate Initial Suggestions ---
    dataset = create_initial_suggestions(dataset)

    # --- Step 3: Setup Manual Annotation Environment ---
    session, task_name = setup_manual_annotation_environment(dataset)

    # --- Step 4: Manual Annotation Instructions ---
    print("\n" + "=" * 60)
    print("🎯 MANUAL ANNOTATION READY!")
    print("=" * 60)
    print("🌐 CVAT is open in your browser")
    print("� FiftyOne app is monitoring your progress")
    print("")
    print("📝 YOUR TASK:")
    print("   • Review EVERY frame manually")
    print("   • Fix/verify ALL bounding boxes")
    print("   • Set vehicle attributes (is_braking, turn_signal)")
    print("   • Add 'DontCare' for ambiguous objects")
    print("   • Ensure 100% perfect ground truth")
    print("")
    print("💡 TIPS:")
    print("   • Use keyboard shortcuts in CVAT for speed")
    print("   • Check FiftyOne app to monitor progress")
    print("   • Take breaks - quality over speed!")
    print("")
    print("🚀 When finished: Close FiftyOne app window to trigger export")
    print("=" * 60)

    # Wait for manual annotation completion
    session.wait()

    # --- Step 5: Automated Export Pipeline ---
    print("\n📦 --- AUTOMATED EXPORT PIPELINE ---")

    # Count final annotations
    final_annotation_count = 0
    for sample in dataset:
        if hasattr(sample, 'frames'):
            for frame in sample.frames.values():
                if hasattr(frame, GROUND_TRUTH_FIELD) and getattr(frame, GROUND_TRUTH_FIELD):
                    final_annotation_count += len(getattr(frame, GROUND_TRUTH_FIELD).detections)

    print(f"📊 Perfect ground truth contains {final_annotation_count} manually verified annotations")

    # Generate timestamp for exports
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # --- Export 1: YOLO Format (for training) ---
    yolo_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_YOLO_Dataset_{timestamp}")
    print(f"🎯 Exporting to YOLO format: {yolo_export_dir}")
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}",
        classes=DETECTION_CLASSES,
        split=0.8  # 80% train, 20% val
    )

    # --- Export 2: COCO Format (for research) ---
    coco_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_COCO_Dataset_{timestamp}")
    print(f"📁 Exporting to COCO format: {coco_export_dir}")
    dataset.export(
        export_dir=coco_export_dir,
        dataset_type=fo.types.COCODetectionDataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}"
    )

    # --- Export 3: Custom Format with Attributes ---
    custom_export_dir = os.path.join(PROJECT_ROOT, f"Perfect_Custom_Dataset_{timestamp}")
    print(f"🔧 Exporting custom format with attributes: {custom_export_dir}")
    export_with_attributes(dataset, custom_export_dir)

    # --- Export 4: Dataset Report ---
    report_path = os.path.join(PROJECT_ROOT, f"Dataset_Report_{timestamp}.html")
    print(f"📊 Generating dataset report: {report_path}")
    generate_dataset_report(dataset, report_path)

    print("\n🎉 --- PERFECT GROUND TRUTH COMPLETE ---")
    print(f"✅ FiftyOne dataset: '{DATASET_NAME}'")
    print(f"✅ YOLO format: {yolo_export_dir}")
    print(f"✅ COCO format: {coco_export_dir}")
    print(f"✅ Custom format: {custom_export_dir}")
    print(f"✅ Dataset report: {report_path}")
    print(f"📊 Total perfect annotations: {final_annotation_count}")
    print("\n🚀 Ready for your dual-stream pipeline!")

# ---
# Utility Functions for Export
# ---
def export_with_attributes(dataset, output_path):
    """Export custom format preserving all vehicle attributes"""

    os.makedirs(output_path, exist_ok=True)

    export_data = {
        'dataset_info': {
            'name': dataset.name,
            'created': datetime.now().isoformat(),
            'total_samples': len(dataset),
            'classes': DETECTION_CLASSES,
            'attributes': VEHICLE_ATTRIBUTES
        },
        'annotations': []
    }

    for sample in dataset:
        sample_data = {
            'video_path': sample.filepath,
            'frames': []
        }

        if hasattr(sample, 'frames'):
            for frame_num, frame in sample.frames.items():
                if hasattr(frame, GROUND_TRUTH_FIELD) and getattr(frame, GROUND_TRUTH_FIELD):
                    frame_data = {
                        'frame_number': frame_num,
                        'detections': []
                    }

                    for detection in getattr(frame, GROUND_TRUTH_FIELD).detections:
                        det_data = {
                            'label': detection.label,
                            'bounding_box': detection.bounding_box,
                            'confidence': getattr(detection, 'confidence', 1.0)
                        }

                        # Preserve vehicle attributes
                        if hasattr(detection, 'is_braking'):
                            det_data['is_braking'] = detection.is_braking
                        if hasattr(detection, 'turn_signal'):
                            det_data['turn_signal'] = detection.turn_signal

                        frame_data['detections'].append(det_data)

                    sample_data['frames'].append(frame_data)

        export_data['annotations'].append(sample_data)

    # Save as JSON
    import json
    with open(f"{output_path}/annotations.json", 'w') as f:
        json.dump(export_data, f, indent=2)

    print(f"✅ Custom format exported with {len(export_data['annotations'])} samples")

def generate_dataset_report(dataset, report_path):
    """Generate HTML report with dataset statistics"""

    # Count statistics
    stats = {
        'total_samples': len(dataset),
        'total_frames': 0,
        'total_annotations': 0,
        'class_counts': {cls: 0 for cls in DETECTION_CLASSES},
        'attribute_counts': {
            'is_braking_true': 0,
            'turn_signal_left': 0,
            'turn_signal_right': 0,
            'turn_signal_hazard': 0
        }
    }

    for sample in dataset:
        if hasattr(sample, 'frames'):
            stats['total_frames'] += len(sample.frames)
            for frame in sample.frames.values():
                if hasattr(frame, GROUND_TRUTH_FIELD) and getattr(frame, GROUND_TRUTH_FIELD):
                    detections = getattr(frame, GROUND_TRUTH_FIELD).detections
                    stats['total_annotations'] += len(detections)

                    for detection in detections:
                        # Count classes
                        if detection.label in stats['class_counts']:
                            stats['class_counts'][detection.label] += 1

                        # Count attributes
                        if hasattr(detection, 'is_braking') and detection.is_braking == 'true':
                            stats['attribute_counts']['is_braking_true'] += 1
                        if hasattr(detection, 'turn_signal'):
                            if detection.turn_signal == 'left':
                                stats['attribute_counts']['turn_signal_left'] += 1
                            elif detection.turn_signal == 'right':
                                stats['attribute_counts']['turn_signal_right'] += 1
                            elif detection.turn_signal == 'hazard':
                                stats['attribute_counts']['turn_signal_hazard'] += 1

    # Generate HTML report
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Perfect Ground Truth Dataset Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ background: #2196F3; color: white; padding: 20px; border-radius: 5px; }}
            .stats {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }}
            .stat-box {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
            .stat-number {{ font-size: 2em; font-weight: bold; color: #2196F3; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎯 Perfect Ground Truth Dataset Report</h1>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Dataset: {dataset.name}</p>
        </div>

        <div class="stats">
            <div class="stat-box">
                <div class="stat-number">{stats['total_samples']}</div>
                <div>Video Samples</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{stats['total_frames']}</div>
                <div>Total Frames</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{stats['total_annotations']}</div>
                <div>Perfect Annotations</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{stats['attribute_counts']['is_braking_true']}</div>
                <div>Braking Events</div>
            </div>
        </div>

        <h2>📊 Class Distribution</h2>
        <ul>
    """

    for cls, count in stats['class_counts'].items():
        html_content += f"<li><strong>{cls}:</strong> {count} instances</li>"

    html_content += f"""
        </ul>

        <h2>🚗 Vehicle Attributes</h2>
        <ul>
            <li><strong>Braking Events:</strong> {stats['attribute_counts']['is_braking_true']}</li>
            <li><strong>Left Turn Signals:</strong> {stats['attribute_counts']['turn_signal_left']}</li>
            <li><strong>Right Turn Signals:</strong> {stats['attribute_counts']['turn_signal_right']}</li>
            <li><strong>Hazard Lights:</strong> {stats['attribute_counts']['turn_signal_hazard']}</li>
        </ul>

        <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 5px;">
            <h3>✅ Quality Assurance</h3>
            <p>This dataset contains <strong>100% manually verified</strong> ground truth annotations.</p>
            <p>Every frame has been reviewed and corrected by human annotators.</p>
            <p>Ready for training robust AI models! 🚀</p>
        </div>
    </body>
    </html>
    """

    with open(report_path, 'w') as f:
        f.write(html_content)

    print(f"✅ Dataset report generated with {stats['total_annotations']} annotations")

if __name__ == "__main__":
    main()