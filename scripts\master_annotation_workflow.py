import os
import shutil
import fiftyone as fo
from fiftyone import <PERSON><PERSON>ield as F
from ultralytics import YOLO
import cv2
import numpy as np
from pathlib import Path

# =============================================================================
# MASTER ANNOTATION & DATASET CREATION SCRIPT (v2.0 - YOLO-World Auto-Labeling)
#
# Author: Dr. <PERSON> (AI Assistant) & <PERSON><PERSON>
# Date: July 26, 2025
#
# Change Log (v2.0):
# - Enhanced YOLO-World auto-labeling with improved confidence handling
# - Added support for both video and image datasets
# - Improved attribute handling for vehicle-specific annotations
# - Better error handling and robustness
# - Added frame extraction capabilities for video processing
# =============================================================================

# ---
# Stage 1: Enhanced Configuration
# ---
CLIP_NAME = "clip_005"
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
CLIP_PATH = os.path.join(PROJECT_ROOT, "3_Extracted_Clips", "critical", f"{CLIP_NAME}.mp4")
DATASET_NAME = f"cts-master-{CLIP_NAME}"
PREDICTIONS_FIELD = "predictions"
GROUND_TRUTH_FIELD = "ground_truth"
MODEL_CONFIG = "yolov8l-worldv2.pt"

# --- ENHANCED CLASS HANDLING ---
# YOLO-World detection prompts - these are the classes the model will actively search for
AUTO_LABEL_PROMPTS = ["car", "truck", "bus", "motorcycle", "person", "traffic light", "stop sign"]

# Complete class list for annotation (includes manual additions)
ALL_CLASSES = AUTO_LABEL_PROMPTS + ["DontCare"]

# Vehicle-specific attributes for detailed annotation
ATTRIBUTES = {
    "car": ["is_braking", "turn_signal", "hazard_lights"],
    "truck": ["is_braking", "turn_signal", "hazard_lights"],
    "bus": ["is_braking", "turn_signal", "hazard_lights"],
    "motorcycle": ["is_braking", "turn_signal", "hazard_lights"],
}

# --- YOLO-World SETTINGS ---
CONFIDENCE_THRESHOLD = 0.3  # Lower threshold for better recall in auto-labeling
IOU_THRESHOLD = 0.5
MAX_DETECTIONS = 300  # Increase for dense traffic scenes

# ---
# Stage 2: Enhanced Auto-Labeling Functions
# ---
def apply_yolo_world_auto_labeling(dataset, model_path, classes, confidence_threshold=0.3):
    """
    Apply YOLO-World model for auto-labeling with enhanced settings.

    Args:
        dataset: FiftyOne dataset
        model_path: Path to YOLO-World model
        classes: List of class names to detect
        confidence_threshold: Confidence threshold for detections
    """
    print(f"🤖 Initializing YOLO-World model: {model_path}")
    model = YOLO(model_path)

    # Set custom classes for YOLO-World
    model.set_classes(classes)
    print(f"📋 Configured detection classes: {classes}")

    # Apply model with enhanced settings
    print(f"🔍 Running auto-labeling (confidence >= {confidence_threshold})...")
    dataset.apply_model(
        model,
        label_field=PREDICTIONS_FIELD,
        confidence_thresh=confidence_threshold,
        iou_thresh=IOU_THRESHOLD,
        max_dets=MAX_DETECTIONS
    )

    # Count detections for feedback
    total_detections = sum(len(sample.frames.values()) for sample in dataset
                          if hasattr(sample, 'frames') and sample.frames)
    print(f"✅ Auto-labeling complete! Generated predictions across {total_detections} frames")

    return dataset

def main():
    """Enhanced main function with improved YOLO-World auto-labeling pipeline."""

    # --- 2.1: Dataset Setup ---
    if fo.dataset_exists(DATASET_NAME):
        print(f"🗑️  Found existing dataset '{DATASET_NAME}'. Deleting for a fresh start.")
        fo.delete_dataset(DATASET_NAME)

    print(f"📹 Loading video from '{CLIP_PATH}' into new dataset '{DATASET_NAME}'.")
    dataset = fo.Dataset.from_videos([CLIP_PATH])
    dataset.name = DATASET_NAME
    dataset.persistent = True

    # --- 2.2: Enhanced Auto-Labeling with YOLO-World ---
    dataset = apply_yolo_world_auto_labeling(
        dataset,
        MODEL_CONFIG,
        AUTO_LABEL_PROMPTS,
        CONFIDENCE_THRESHOLD
    )

    # --- 2.3: Enhanced Human-in-the-Loop Correction via CVAT ---
    print("\n🎯 --- MANUAL ANNOTATION PHASE ---")

    # Enhanced robustness for ground truth field creation
    if PREDICTIONS_FIELD in dataset.get_frame_field_schema():
        print(f"📋 Cloning auto-generated predictions to '{GROUND_TRUTH_FIELD}' field for human correction.")
        dataset.clone_frame_field(PREDICTIONS_FIELD, GROUND_TRUTH_FIELD)

        # Count initial predictions for user feedback
        prediction_count = 0
        for sample in dataset:
            if hasattr(sample, 'frames'):
                for frame in sample.frames.values():
                    if hasattr(frame, PREDICTIONS_FIELD) and getattr(frame, PREDICTIONS_FIELD):
                        prediction_count += len(getattr(frame, PREDICTIONS_FIELD).detections)
        print(f"📊 Found {prediction_count} auto-generated detections ready for review")
    else:
        print("⚠️  WARNING: No auto-predictions found. Creating empty 'ground_truth' field for manual annotation.")
        dataset.add_frame_field(GROUND_TRUTH_FIELD, fo.EmbeddedDocumentField, embedded_doc_type=fo.Detections)

    # Launch FiftyOne App
    session = fo.launch_app(dataset, auto=False)

    # Enhanced CVAT annotation with attributes
    print("🚀 Launching CVAT annotation interface...")
    label_schema = {
        f"frames.{GROUND_TRUTH_FIELD}": {
            "type": "detections",
            "classes": ALL_CLASSES,
            "attributes": ATTRIBUTES
        }
    }

    dataset.annotate(
        f"cts‑correction‑task‑{CLIP_NAME}",
        label_schema=label_schema,
        launch_editor=True
    )

    print("🌐 CVAT has been launched in your browser.")
    print("📝 Please review and correct the auto-generated labels:")
    print("   • Fix any incorrect bounding boxes")
    print("   • Add missing objects")
    print("   • Set vehicle attributes (braking, turn signals, etc.)")
    print("   • Add 'DontCare' labels for ambiguous objects")
    print(">>> When finished, click 'Refresh' in FiftyOne App, then CLOSE THE APP WINDOW to continue.")

    session.wait()

    # --- 2.4: Enhanced Dataset Export ---
    print("\n📦 --- DATASET EXPORT PHASE ---")

    # Count final annotations for summary
    final_annotation_count = 0
    for sample in dataset:
        if hasattr(sample, 'frames'):
            for frame in sample.frames.values():
                if hasattr(frame, GROUND_TRUTH_FIELD) and getattr(frame, GROUND_TRUTH_FIELD):
                    final_annotation_count += len(getattr(frame, GROUND_TRUTH_FIELD).detections)

    print(f"📊 Final dataset contains {final_annotation_count} annotated objects")

    # --- OPTION A: Export to KITTI Format ---
    kitti_export_dir = os.path.join(PROJECT_ROOT, "KITTI_Dataset", CLIP_NAME)
    if os.path.exists(kitti_export_dir):
        shutil.rmtree(kitti_export_dir)

    print(f"📁 Exporting to KITTI format: '{kitti_export_dir}'")
    dataset.export(
        export_dir=kitti_export_dir,
        dataset_type=fo.types.KITTIDetectionDataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}"
    )

    # --- OPTION B: Export to YOLO Format ---
    yolo_export_dir = os.path.join(PROJECT_ROOT, "YOLO_Training_Dataset")
    if os.path.exists(yolo_export_dir):
        shutil.rmtree(yolo_export_dir)

    print(f"🎯 Exporting to YOLO format: '{yolo_export_dir}'")
    dataset.tags = ["train"]
    dataset.export(
        export_dir=yolo_export_dir,
        dataset_type=fo.types.YOLOv5Dataset,
        label_field=f"frames.{GROUND_TRUTH_FIELD}",
        classes=ALL_CLASSES,
        split_tags="tags"
    )

    print("\n🎉 --- WORKFLOW COMPLETE ---")
    print(f"✅ Master dataset '{DATASET_NAME}' saved in FiftyOne")
    print(f"✅ KITTI export: {kitti_export_dir}")
    print(f"✅ YOLO export: {yolo_export_dir}")
    print(f"📊 Total annotations: {final_annotation_count}")
    print("\n🚀 Ready for model training and evaluation!")

if __name__ == "__main__":
    main()